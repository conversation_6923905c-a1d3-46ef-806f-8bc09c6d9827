package com.deeppaas.rule.param;

import lombok.Data;
import org.springframework.data.relational.core.sql.In;

@Data
public class CheckImageHtmlResultParam {
    // 空白图片审查 flag_blank
    private Boolean blank;
    // 重复图片审查
    private Boolean repeatImage;
    // 文本方向 flag_house_angle
    private Boolean houseAngle;

    // 倾斜 flag_rectify
    private Boolean bias;
    // 倾斜角容错
    private String rectify;

    // 黑边 flag_edge_remove
    private Boolean edgeRemove;

    // 污点容错 flag_stain
    private Boolean stain;

    private Integer stainValue;
    // 装订孔容错 flag_hole
    private Boolean bindingHole;

    private Integer hole;
    // DPI检查 flag_dpi
    private Boolean dpi;
    // DPI值
    private String dpiValue;
    // 格式检查 flag_format
    private Boolean format;
    private String[] formatList; // 'JPEG', 'JPEG' ,'TIFF', 'PDF', 'GIF',  'RAW','BMP', 'FPX', 'PNG'
    // 数量统计
    private Boolean counting;
    // KB值检查 flag_kb
    private Boolean kb;
    // KB最小值
    private Integer minKB;
    // KB最大值
    private Integer maxKB;
    // '空文件夹检查'
    private Boolean blankFilesCheck;
    // 篇幅统计 flag_page_size
    private Boolean pageSize;
    // 条目数量图片一致性检查
    private Boolean excelImageAccord;
    // 连续性审查
    private Boolean continuity;

    // PDF文件质量审查
    private String[] pdfCheck;
    //PDF图像与图片一致性
    private Boolean pdfImageUniformity;
    //是否PDF存在文字层
    private Boolean pdfHasTextCheck;

    private Boolean ofdUniformity;

    /**
     * 检查图片名
     * */
    private String reImageName;

    /**
     * 检查路径
     * */
    private String reImagePath;

    private Boolean imageQuality;

    private Integer imageScore;




}
