package com.deeppaas.rule.factory.impl.image;


import com.deeppaas.FileEnum;
import com.deeppaas.ai.aiClient.AiClient;
import com.deeppaas.ai.aiResult.CheckImageAiResultDTO;
import com.deeppaas.common.helper.*;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.enums.ErrorResultType;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.rule.enums.RuleImageEnums;
import com.deeppaas.rule.factory.RuleExecuteFactory;
import com.deeppaas.rule.factory.base.RuleExecuteFactoryBase;
import com.deeppaas.rule.param.CheckImageHtmlResultParam;
import com.deeppaas.rule.service.RuleExecuteFactoryService;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskPdfDataDTO;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.data.service.ProjectTaskPdfDataService;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.work.enums.TaskStatusEnums;
import com.itextpdf.text.pdf.PdfReader;
import com.yh.scofd.agent.HTTPAgent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 图像检查
 */
@Slf4j
@Service("rule_execute_imageCheck")
public class ImageChechRule extends RuleExecuteFactoryBase implements RuleExecuteFactoryService {

    @Autowired
    private AiClient aiClient;
    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectTaskImageDataService imageDataService;
    @Autowired
    private ProjectTaskPdfDataService pdfDataService;
    @Autowired
    private TaskErrorResultService taskErrorResultService;
    @Autowired
    @Lazy
    private RuleExecuteFactory ruleExecuteFactory;
    @Autowired
    private ExecutorService executorService;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Task implements Runnable {
        private PublicRuleDTO ruleDTO;
        private ProjectTaskImageDataDTO imageDataDO;
        private CheckImageHtmlResultParam param;

        private List<ProjectTaskPdfDataDTO> taskPdfDataDTO;
        private List<ProjectTaskImageDataDTO> taskImageDataDTO;
        private int no;
        private int time;


        @Override
        public void run() {

            aiCheck(ruleDTO, imageDataDO, param, taskPdfDataDTO, taskImageDataDTO);
        }
    }

    //0007 0004 0007 0006 0012 0004 0006 0003 0012 0002 0009 0005 0003 0002 0009 0005 0001 0001
    public void aiCheck(PublicRuleDTO ruleDTO, ProjectTaskImageDataDTO imageDataDO, CheckImageHtmlResultParam param, List<ProjectTaskPdfDataDTO> taskPdfDataDTO, List<ProjectTaskImageDataDTO> taskImageDataDTO) {
        try {

            ProjectTaskInfoDTO projectTaskInfoDTO = projectTaskInfoService.findById(imageDataDO.getTaskId());
            if (Objects.equals(projectTaskInfoDTO.getTaskStatus(), TaskStatusEnums.STOP.getNum()) || Objects.equals(projectTaskInfoDTO.getTaskStatus(), TaskStatusEnums.ERROR.getNum()))
                return;

            List<TaskErrorResultDO> lists = Lists.newArrayList();
            String imagePath = imageDataDO.getImageFilePath();
            List<String> imageNames = imageDataDO.getImageNames();
            Map<String, Integer> pageSizeMap = new HashMap<>();
            Map<String, Integer> suffixMap = new HashMap<>();
            Map<String, ProjectTaskPdfDataDTO> projectTaskPdfDataDTOMap = taskPdfDataDTO.stream().collect(Collectors.toMap(item -> item.getDataKey() + "_" + item.getPartNumber(), Function.identity()));
            try {

                for (int i = 0; i < imageNames.size(); i++) {
                    File image = new File(imagePath + File.separator + imageNames.get(i));

                    if (image.isFile() && FileHelper.isImage(image.getAbsolutePath()) && ImageHelper.isImageValid(image)) {
                        //程序不在进行中时需要退出
                        if (!Objects.equals(projectTaskInfoDTO.getTaskStatus(), TaskStatusEnums.ING.getNum())) {
                            return;
                        }
                        CheckImageAiResultDTO checkImageAiResultDTO = null;
                        AtomicInteger errorCount = new AtomicInteger(0); // 错误计数器
                        //开始质检
                        checkImageAiResultDTO = aiClient.set(image, param, projectTaskInfoDTO);
                        while (checkImageAiResultDTO == null){
                            // 返回内容为空继续重复检查
                            checkImageAiResultDTO = aiClient.set(image, param, projectTaskInfoDTO);
                            if (errorCount.incrementAndGet() % 10 == 1)
                                log.error("检测失败[" + errorCount + "]，" + image.getAbsolutePath() + "重新尝试，如反复重启AI服务仍未解决请停止系统并重启！");
                        }

                        String pageSize = checkImageAiResultDTO.getPageSize();
                        if (StringHelper.isNotEmpty(pageSize)) {
                            Integer index = pageSizeMap.get(pageSize);
                            if (index == null) {
                                index = 1;
                            } else {
                                index = index + 1;
                            }
                            pageSizeMap.put(checkImageAiResultDTO.getPageSize(), index);
                        }
                        String suffix = FileHelper.getSuffix(image);
                        Integer index2 = suffixMap.get(suffix);
                        if (StringHelper.isNotEmpty(suffix)) {
                            if (index2 == null) {
                                index2 = 1;
                            } else {
                                index2 = index2 + 1;
                            }
                            suffixMap.put(suffix, index2);
                        }
                        lists.addAll(buildAicheckResult(param, imageDataDO, checkImageAiResultDTO, image));

                    } else {
                        TaskErrorResultDO errorResultDO = buildError(imageDataDO, RuleImageEnums.DAMAGE, RuleImageEnums.DAMAGE.getValueName(), image, null, BoolHelper.INT_TRUE);
                        lists.add(errorResultDO);
                    }


                }

                //图像路径错误

                //checkPdf
                if (BoolHelper.boolToB(param.getPdfImageUniformity()) || BoolHelper.boolToB(param.getPdfHasTextCheck())) {
                    ProjectTaskPdfDataDTO taskPdfData = projectTaskPdfDataDTOMap.get(imageDataDO.getDataKey() + "_" + imageDataDO.getPartNumber());
                    if (taskPdfData != null) {
                        checkPdf(taskPdfData, imageDataDO);
                        taskPdfData.setMateIs(BoolHelper.INT_TRUE);
                        pdfDataService.save(taskPdfData);
                    }
                }
                //TODO: 实现从param获取是否检查双层pdf的标志
                if ()

                //checkofd
                if (BoolHelper.boolToB(param.getOfdUniformity())) {
                    ProjectTaskPdfDataDTO taskPdfData = projectTaskPdfDataDTOMap.get(imageDataDO.getDataKey() + "_" + imageDataDO.getPartNumber());
                    if (taskPdfData != null) {
                        checkOfd(taskPdfData, imageDataDO);
                        taskPdfData.setMateIs(BoolHelper.INT_TRUE);
                        pdfDataService.save(taskPdfData);
                    } else {
                        TaskErrorResultDO errorResultDO = TaskErrorResultDO.builder().taskId(imageDataDO.getTaskId()).taskConfigId(imageDataDO.getTaskConfigId()).ruleName("OFD图像匹配检查").ruleType("OFD匹配审查").errorType(ErrorResultType.IMAGE.getNum())

                                .errorCoordinate(null).aiCheck(BoolHelper.INT_TRUE).dataKey(imageDataDO.getDataKey()).build();
                        taskErrorResultService.save(errorResultDO);
                    }


                }


                imageDataDO.setSuffixCount(JsonHelper.toJson(suffixMap));
                imageDataDO.setSuccess(BoolHelper.INT_TRUE);
                imageDataDO.setPageSizeCount(JsonHelper.toJson(pageSizeMap));
                //              projectTaskInfoDTO = projectTaskInfoService.findById(imageDataDO.getTaskId());
                //程序不在进行中时需要退出
//                if (!Objects.equals(projectTaskInfoDTO.getTaskStatus(), TaskStatusEnums.ING.getNum())) {
//                    return;
//                }
                taskErrorResultService.saves(lists);


                imageDataService.save(imageDataDO);
                //更新进度条
                projectTaskInfoService.updateTaskProcess(imageDataDO.getTaskId(), new BigDecimal(0), FileEnum.C.getNum());
            } catch (Exception e) {
                ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(imageDataDO.getTaskId());
                taskInfoDTO.setErrorInfo("图像质检失败，请检查Ai是否正常启动");
                taskInfoDTO.setTaskStatus(TaskStatusEnums.ERROR.getNum());
                projectTaskInfoService.save(taskInfoDTO);
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private TaskErrorResultDO checkOfd(ProjectTaskPdfDataDTO taskPdfData, ProjectTaskImageDataDTO imageDataDO) {

        //图像名称连续性检查 -  _
        TaskErrorResultDO errorResultDO = null;
        //  String imageFile = imageDataDO.getImageFilePath();
        List<String> names = imageDataDO.getImageNames();
        //   File file = new File(imageFile);
        // List<File> files = Arrays.stream(file.listFiles()).toList().stream().sorted(new AlphanumComparator()).collect(Collectors.toList());
        HTTPAgent ha = null;
        if (taskPdfData != null) {

            String pdfPath = taskPdfData.getPdfFilePath();
            File ofdFile = new File(pdfPath);
            pdfPath = ofdFile.getParent() + File.separator + FileHelper.getFileName(ofdFile) + ".pdf";

            try {
                ha = new HTTPAgent("http://localhost:9000/v1/");
                ha.OFDToPDF(ofdFile, new FileOutputStream(pdfPath));
            } catch (Exception e) {
                errorResultDO = TaskErrorResultDO.builder().taskId(imageDataDO.getTaskId()).taskConfigId(imageDataDO.getTaskConfigId()).ruleName("OFD图像一致性审查-未找到OFD工具").ruleType("OFD审查").errorType(ErrorResultType.IMAGE.getNum()).errorFileValue(taskPdfData.getPdfFilePath()).errorCoordinate(null).aiCheck(BoolHelper.INT_TRUE).dataKey(taskPdfData.getPdfFilePath()).build();
                taskErrorResultService.save(errorResultDO);
                e.printStackTrace();
            }


            File pdfFile = new File(pdfPath);
            PDDocument pdDocument = null;
            try {
                pdDocument = PDDocument.load(pdfFile);
                int pages = pdDocument.getNumberOfPages();
                if (pages != names.size()) {
                    errorResultDO = TaskErrorResultDO.builder().taskId(imageDataDO.getTaskId()).taskConfigId(imageDataDO.getTaskConfigId()).ruleName("OFD图像一致性审查").ruleType("OFD审查").errorType(ErrorResultType.IMAGE.getNum()).errorFileValue(taskPdfData.getPdfFilePath()).errorCoordinate(null).aiCheck(BoolHelper.INT_TRUE).dataKey(taskPdfData.getPdfFilePath()).build();
                    taskErrorResultService.save(errorResultDO);
                    return errorResultDO;
                }

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                pdfFile.delete();
                if (pdDocument != null) {
                    try {
                        if (ha != null) {
                            ha.close();
                        }
                        pdDocument.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

            }
        }

        return errorResultDO;

    }


    public static void imageToOdf(List<File> sourceFileList, OutputStream targetOFDFileOut) throws Exception {
        HTTPAgent ha = new HTTPAgent("http://192.168.10.116:9000/v1/");

        /**为生成OFD文档设置元数据*/
        ha.imagesToOFD(sourceFileList, targetOFDFileOut, 300);
    }

    private TaskErrorResultDO[] checkPdf(ProjectTaskPdfDataDTO taskPdfData, ProjectTaskImageDataDTO imageDataDO) {
        //图像名称连续性检查 -  _
        TaskErrorResultDO[] errorResultDOs = new TaskErrorResultDO[2];
  //      String imageFile = imageDataDO.getImageFilePath();
        List<String> names = imageDataDO.getImageNames();
    //    File file = new File(imageFile);
     //   List<File> files = Arrays.stream(file.listFiles()).toList().stream().sorted(new AlphanumComparator()).collect(Collectors.toList());
        if (taskPdfData != null && !CollectionUtils.isEmpty(names) && (taskPdfData.getFileType() == null || taskPdfData.getFileType().equals("pdf"))) {
            String pdfPath = taskPdfData.getPdfFilePath();
            PDDocument pdDocument = null;
            PdfReader reader = null;
            try {
                pdDocument = PDDocument.load(new File(pdfPath));
                int pages = pdDocument.getNumberOfPages();
                if (pages != names.size()) {
                    String errorValue = "PDF页数：" + pages +"，图片总数："+names.size();
                    TaskErrorResultDO errorResultDO = TaskErrorResultDO.builder().taskId(imageDataDO.getTaskId()).taskConfigId(imageDataDO.getTaskConfigId()).ruleName("pdf图像一致性审查").ruleType("PDF审查").errorType(ErrorResultType.IMAGE.getNum()).errorFileValue(errorValue).errorCoordinate(null).aiCheck(BoolHelper.INT_TRUE).dataKey(taskPdfData.getPdfFilePath()).build();
                    taskErrorResultService.save(errorResultDO);
                    errorResultDOs[0] = errorResultDO;
                }
                // 双层PDF检查是否含有文字层 TODO
                int pdfPageNum = 1;
                for(PDPage page : pdDocument.getPages()) {
                    boolean hasText = true;
                    try {
                        PDFTextStripper stripper = new PDFTextStripper();
                        String text = stripper.getText(pdDocument);
                        if(text.trim().isEmpty()) {
                            hasText = false;
                        }
                    } catch (IOException e) {
                        hasText = false;
                    } finally {
                        if (!hasText) {
                            String errorValue = "第" + pdfPageNum + "页未检测到文字层或内容为空";
                            TaskErrorResultDO errorResultDO = TaskErrorResultDO.builder().taskId(imageDataDO.getTaskId()).taskConfigId(imageDataDO.getTaskConfigId()).ruleName("pdf文字层审查").ruleType("PDF审查").errorType(ErrorResultType.IMAGE.getNum()).errorFileValue(errorValue).errorCoordinate(null).aiCheck(BoolHelper.INT_TRUE).dataKey(taskPdfData.getPdfFilePath()).build();
                            taskErrorResultService.save(errorResultDO);
                            errorResultDOs[1] = errorResultDO;
                        }
                    }
                    pdfPageNum++;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (pdDocument != null) {
                    try {
                        pdDocument.close();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (reader != null) reader.close();
            }
        }
        return errorResultDOs;

    }

    @Override
    public List<TaskErrorResultDO> ruleExecute(PublicRuleDTO ruleDTO, List<ProjectTaskFormDataDTO> formDataDTOList, List<ProjectTaskImageDataDTO> taskImageDataDTOList, ProjectTaskInfoDTO projectTaskInfoDTO, ProjectTaskPdfDataDTO taskPdfDataDTO, ProjectTaskConfigDTO taskConfigDTO){
        String ruleValue = ruleDTO.getRuleValue();
        List<TaskErrorResultDO> allList = Lists.newArrayList();
        ScheduledExecutorService executorService1 = Executors.newScheduledThreadPool(4);
        if (Objects.equals(ruleDTO.getRuleType(), FileEnum.C.getNum())) {
            if (!StringHelper.isEmpty(ruleValue)) {
                try {
                    CheckImageHtmlResultParam param = JsonHelper.fromJson(ruleValue, CheckImageHtmlResultParam.class);
                    while (!aiClient.open(param)) {
                        // 参数设置失败，重新设置直到成功
                        Thread.sleep(3 * 1000);
                        aiClient.open(param);
                    }
                    Integer exactnessScale = projectTaskInfoDTO.getExactnessScale();
                    int scale = Math.round(taskImageDataDTOList.size() * exactnessScale / 100);
                    taskImageDataDTOList = taskImageDataDTOList.subList(0, scale);
                    //java检查部分图像规则
                    allList.addAll(buildRulecheckResult(ruleDTO, formDataDTOList, taskImageDataDTOList, param, projectTaskInfoDTO, taskConfigDTO));

                    List<ProjectTaskPdfDataDTO> taskPdfDataDTOs = pdfDataService.findByTaskId(projectTaskInfoDTO.getId());
                    for (int i = 0; i < taskImageDataDTOList.size(); i++) {
                        if (Objects.equals(projectTaskInfoDTO.getTaskStatus(), TaskStatusEnums.STOP.getNum()))
                            return allList;
                        ProjectTaskImageDataDTO imageDataDO = taskImageDataDTOList.get(i);
                        imageDataDO.setMateIs(BoolHelper.INT_FALSE);
                        ProjectTaskFormDataDTO projectTaskFormDataDTO = formDataDTOList.stream().filter(item -> Objects.equals(item.getDataKey(), imageDataDO.getDataKey())).findFirst().orElse(null);
                        if (projectTaskFormDataDTO != null) {
                            imageDataDO.setMateIs(BoolHelper.INT_TRUE);
                        }
                        if (i == 1) {
                            Thread.sleep(4 * 1000);
                        }

                        if (Objects.equals(imageDataDO.getSuccess(), BoolHelper.INT_FALSE)) {
                            int lifeTime = (int) (Math.random() * 9 + 1);
                            executorService1.execute(new Task(ruleDTO, imageDataDO, param, taskPdfDataDTOs, taskImageDataDTOList, i, lifeTime));

                        }
                    }
                } catch(InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return allList;
    }


    /**
     * 图像代码规则审查
     */
    private List<TaskErrorResultDO> buildRulecheckResult(PublicRuleDTO ruleDTO, List<ProjectTaskFormDataDTO> formDataDTOList, List<ProjectTaskImageDataDTO> taskImageDataDTOList, CheckImageHtmlResultParam param, ProjectTaskInfoDTO projectTaskInfoDTO, ProjectTaskConfigDTO taskConfigDTO) {
        List<TaskErrorResultDO> list = Lists.newArrayList();


        if (BoolHelper.boolToB(param.getRepeatImage())) {
            //重复图像审查
            RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(RuleImageEnums.REPEATIMAGE.getCode());
            list.addAll(executeFactoryService.ruleExecute(ruleDTO, formDataDTOList, taskImageDataDTOList, projectTaskInfoDTO, null, taskConfigDTO));
        }

        if (BoolHelper.boolToB(param.getBlankFilesCheck())) {
            //空文件夹-
            RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(RuleImageEnums.BLANKFILESCHECK.getCode());
            list.addAll(executeFactoryService.ruleExecute(ruleDTO, formDataDTOList, taskImageDataDTOList, projectTaskInfoDTO, null, taskConfigDTO));

        }


        if (BoolHelper.boolToB(param.getContinuity())) {
            //图像名称连续性检查 -  _
            RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(RuleImageEnums.CONTINUITY.getCode());
            list.addAll(executeFactoryService.ruleExecute(ruleDTO, formDataDTOList, taskImageDataDTOList, projectTaskInfoDTO, null, taskConfigDTO));

        }

        //图像路径检查
        if (StringHelper.isNotEmpty(param.getReImagePath())) {
            ruleDTO.setRuleDescribe(param.getReImagePath());
            RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(RuleImageEnums.REIMAGEPATH.getCode());
            list.addAll(executeFactoryService.ruleExecute(ruleDTO, formDataDTOList, taskImageDataDTOList, projectTaskInfoDTO, null, taskConfigDTO));

        }
        //图像路径名称
        if (StringHelper.isNotEmpty(param.getReImageName())) {

            ruleDTO.setRuleDescribe(param.getReImageName());
            RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(RuleImageEnums.REIMAGENAME.getCode());
            list.addAll(executeFactoryService.ruleExecute(ruleDTO, formDataDTOList, taskImageDataDTOList, projectTaskInfoDTO, null, taskConfigDTO));

        }


        return list;
    }


    //TODO 三种构造方式后期可以融合成代理模式，项目紧急又变更快 暂时不处理
    private List<TaskErrorResultDO> buildAicheckResult(CheckImageHtmlResultParam param, ProjectTaskImageDataDTO formDataDO, CheckImageAiResultDTO ai, File url) {
        /********************************************Ai合规审查***********************************************************/
        List<TaskErrorResultDO> lists = Lists.newArrayList();

        if (BoolHelper.boolToB(param.getBlank())) {
            //空白图片
            if (BoolHelper.boolToB(ai.getBlank())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.BLANK, BoolHelper.INT_FALSE, url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }
        if (BoolHelper.boolToB(param.getHouseAngle())) {
            //文本方向
            if (NumeralrHelper.isNotNull(ai.getHouseAngle())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.HOUSEANGLE, ai.getHouseAngle(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }
        if (BoolHelper.boolToB(param.getBias())) {
            //倾斜
            if (NumeralrHelper.isNotNull(ai.getRectify())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.BIAS, ai.getRectify(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }
        if (BoolHelper.boolToB(param.getEdgeRemove())) {
            //黑边
            if (!CollectionUtils.isEmpty(ai.getEdgeRemove())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.EDGEREMOVE, JsonHelper.toJson(ai.getEdgeRemove()), url, JsonHelper.toJson(ai.getEdgeRemove()), BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }

        }
        if (BoolHelper.boolToB(param.getStain())) {
            //污点容错
            if (!CollectionUtils.isEmpty(ai.getStain())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.STAINVALUE, "污点", url, JsonHelper.toJson(ai.getStain()), BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }


        if (BoolHelper.boolToB(param.getBindingHole())) {
            //装订孔容错
            if (!CollectionUtils.isEmpty(ai.getHole())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.HOLE, "装订孔", url, JsonHelper.toJson(ai.getHole()), BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }

        if (BoolHelper.boolToB(param.getDpi())) {
            //dpi
            if (NumeralrHelper.isNotNull(ai.getDpi())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.DPI, ai.getDpi(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }
        if (BoolHelper.boolToB(param.getFormat())) {
            //格式检查
            if (BoolHelper.boolToB(ai.getFormat())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.FORMAT, ai.getFormat(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }

        if (BoolHelper.boolToB(param.getKb())) {
            //kb检查
            if (NumeralrHelper.isNotNull(ai.getKb())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.KB, ai.getKb(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }

        if (BoolHelper.boolToB(param.getImageQuality())) {
            //压缩率检查
            if (NumeralrHelper.isNotNull(ai.getQuality())) {
                TaskErrorResultDO errorResultDO = buildError(formDataDO, RuleImageEnums.QUALITY, ai.getQuality(), url, null, BoolHelper.INT_TRUE);
                lists.add(errorResultDO);
            }
        }

        return lists;
    }


}
